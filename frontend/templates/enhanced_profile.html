{% extends 'base.html' %}

{% block title %}Enhanced User Profile - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="welcome-header text-center mb-4">
    <h2 class="fw-bold">
        <i class="fas fa-rocket me-2 text-success"></i>Enhanced User Profile
    </h2>
    <div class="badge bg-warning text-dark mb-2">Test (UAT) Environment</div>
    <div class="badge bg-success text-white mb-3">
        <i class="fas fa-tachometer-alt me-1"></i>Optimized Performance
    </div>
</div>

<!-- Performance Metrics Card -->
<div class="card border-0 shadow-sm mb-4 border-start border-success border-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>Performance Metrics
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-success mb-1">{{ "%.3f"|format(page_load_time) }}s</div>
                    <small class="text-muted">Page Load Time</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-primary mb-1">{{ "%.1f"|format(performance_stats.cache_hit_rate) }}%</div>
                    <small class="text-muted">Cache Hit Rate</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-info mb-1">{{ "%.3f"|format(performance_stats.average_response_time) }}s</div>
                    <small class="text-muted">Avg Response Time</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-warning mb-1">{{ performance_stats.total_requests }}</div>
                    <small class="text-muted">Total Requests</small>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-success" role="progressbar"
                         style="width: {{ performance_stats.cache_hit_rate }}%"
                         aria-valuenow="{{ performance_stats.cache_hit_rate }}"
                         aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
                <small class="text-muted">Cache Efficiency</small>
            </div>
        </div>
    </div>
</div>

<!-- Comparison Banner -->
<div class="alert alert-info border-0 shadow-sm mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="alert-heading mb-2">
                <i class="fas fa-info-circle me-2"></i>Performance Comparison
            </h6>
            <p class="mb-0">
                This enhanced profile service demonstrates optimized caching and reduced API calls.
                Compare with the <a href="{{ url_for('profile') }}" class="alert-link">original profile page</a>
                to see the performance difference.
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('profile') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-user me-1"></i>Original Profile
            </a>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-id-card me-2"></i>Basic Information
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">First Name</label>
                    <div class="form-control-plaintext">{{ user_profile.firstName }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Last Name</label>
                    <div class="form-control-plaintext">{{ user_profile.lastName }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Display Name</label>
                    <div class="form-control-plaintext">{{ user_profile.displayName }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Person ID</label>
                    <div class="form-control-plaintext">{{ user_profile.personid }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Username</label>
                    <div class="form-control-plaintext">{{ user_profile.userName }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Login ID</label>
                    <div class="form-control-plaintext">{{ user_profile.loginID }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Login Username</label>
                    <div class="form-control-plaintext">{{ user_profile.loginUserName }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Email</label>
                    <div class="form-control-plaintext">{{ user_profile.email }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-map-marker-alt me-2"></i>Location & Contact
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Country</label>
                    <div class="form-control-plaintext">{{ user_profile.country }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">State/Province</label>
                    <div class="form-control-plaintext">{{ user_profile.stateprovince }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Phone</label>
                    <div class="form-control-plaintext">{{ user_profile.phone }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Primary Phone</label>
                    <div class="form-control-plaintext">{{ user_profile.primaryPhone }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Time Zone</label>
                    <div class="form-control-plaintext">{{ user_profile.timezone }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">System Time Zone</label>
                    <div class="form-control-plaintext">{{ user_profile.systimezone }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>System Settings
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Base Language</label>
                    <div class="form-control-plaintext">{{ user_profile.baseLang }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Base Currency</label>
                    <div class="form-control-plaintext">{{ user_profile.baseCurrency }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Base Calendar</label>
                    <div class="form-control-plaintext">{{ user_profile.baseCalendar }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Date Format</label>
                    <div class="form-control-plaintext">{{ user_profile.dateformat }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Can Use Inactive Sites</label>
                    <div class="form-control-plaintext">{{ user_profile.canUseInactiveSites }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Storeroom</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultStoreroom }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Repair Site</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultRepairSite or 'None' }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Repair Facility</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultRepairFacility or 'None' }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-building me-2"></i>Organization & Site Settings
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Organization</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultOrg }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Site Description</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultSiteDescription }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Storeroom Site</label>
                    <div class="form-control-plaintext">{{ user_profile.defaultStoreroomSite or 'None' }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">Insert Site</label>
                    <div class="form-control-plaintext">
                        {{ user_profile.insertSite }}
                        {% if available_sites %}
                            <small class="text-muted d-block">
                                ({{ available_sites|length }} sites available)
                            </small>
                        {% endif %}
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Default Site</label>
                    <div class="form-control-plaintext">
                        {{ user_profile.defaultSite }}
                        {% if available_sites %}
                            <small class="text-muted d-block">
                                ({{ available_sites|length }} sites available)
                            </small>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Site Access Information Card with Tabs -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-key me-2"></i>Site Access Information
        </h5>
    </div>
    <div class="card-body p-0">
        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="siteAccessTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="person-tab" data-bs-toggle="tab" data-bs-target="#person" type="button" role="tab">
                    <i class="fas fa-user me-1"></i>Person
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="maxuser-tab" data-bs-toggle="tab" data-bs-target="#maxuser" type="button" role="tab">
                    <i class="fas fa-user-cog me-1"></i>User Account
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="groups-tab" data-bs-toggle="tab" data-bs-target="#groups" type="button" role="tab">
                    <i class="fas fa-users me-1"></i>Group Memberships
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sites-tab" data-bs-toggle="tab" data-bs-target="#sites" type="button" role="tab">
                    <i class="fas fa-building me-1"></i>Site Authorizations
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="siteAccessTabContent">
            <!-- Person Tab -->
            <div class="tab-pane fade show active" id="person" role="tabpanel">
                <div class="p-4">
                    <div id="personData">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading person information...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MaxUser Tab -->
            <div class="tab-pane fade" id="maxuser" role="tabpanel">
                <div class="p-4">
                    <div id="maxuserData">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading user account information...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Groups Tab -->
            <div class="tab-pane fade" id="groups" role="tabpanel">
                <div class="p-4">
                    <div id="groupsData">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading group memberships...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sites Tab -->
            <div class="tab-pane fade" id="sites" role="tabpanel">
                <div class="p-4">
                    <div id="sitesData">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading site authorizations...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Available Sites Card (Original) -->
{% if available_sites %}
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Available Sites ({{ available_sites|length }})
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            {% for site in available_sites[:12] %}  <!-- Show first 12 sites -->
            <div class="col-md-4 mb-2">
                <div class="border rounded p-2 bg-light">
                    <strong>{{ site.siteid }}</strong>
                    {% if site.description != site.siteid %}
                        <br><small class="text-muted">{{ site.description }}</small>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% if available_sites|length > 12 %}
        <div class="text-center mt-3">
            <small class="text-muted">... and {{ available_sites|length - 12 }} more sites</small>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Performance Tips Card -->
<div class="card border-0 shadow-sm mb-4 border-start border-warning border-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-lightbulb me-2"></i>Performance Optimizations
        </h5>
    </div>
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-memory text-success me-2"></i>Memory Caching</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i> Profile data cached in memory</li>
                    <li><i class="fas fa-check text-success me-1"></i> Sites data cached efficiently</li>
                    <li><i class="fas fa-check text-success me-1"></i> Session validation cached</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-database text-primary me-2"></i>Smart API Usage</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-primary me-1"></i> Single API call strategy</li>
                    <li><i class="fas fa-check text-primary me-1"></i> Optimized timeouts</li>
                    <li><i class="fas fa-check text-primary me-1"></i> Intelligent fallbacks</li>
                </ul>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-light border mb-0">
                    <small>
                        <i class="fas fa-info-circle text-info me-1"></i>
                        <strong>Expected Performance:</strong> 60-80% faster than traditional profile loading with
                        reduced API calls and intelligent caching strategies.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-4 mb-5">
    <a href="{{ url_for('welcome') }}" class="btn btn-outline-primary me-2">
        <i class="fas fa-arrow-left me-2"></i>Back to Welcome
    </a>
    <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary me-2">
        <i class="fas fa-user me-2"></i>Original Profile
    </a>
    <button id="refreshProfile" class="btn btn-outline-success me-2">
        <i class="fas fa-sync-alt me-2"></i>Refresh Profile
    </button>
    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
        <i class="fas fa-sign-out-alt me-2"></i>Logout
    </a>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Refresh profile functionality
        const refreshButton = document.getElementById('refreshProfile');

        refreshButton.addEventListener('click', function() {
            const originalText = refreshButton.innerHTML;
            refreshButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Refreshing...';
            refreshButton.disabled = true;

            // Add cache-busting parameter to force refresh
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('refresh', Date.now());

            // Reload the page with cache-busting parameter
            window.location.href = currentUrl.toString();
        });

        // Auto-refresh performance stats every 30 seconds
        setInterval(function() {
            // Add a subtle indicator that stats are being updated
            const metricsCard = document.querySelector('.card-header.bg-success');
            if (metricsCard) {
                metricsCard.style.opacity = '0.8';
                setTimeout(() => {
                    metricsCard.style.opacity = '1';
                }, 200);
            }
        }, 30000);

        // Log performance metrics to console for debugging
        console.log('Enhanced Profile Performance Metrics:', {
            pageLoadTime: {{ page_load_time }},
            cacheHitRate: {{ performance_stats.cache_hit_rate }},
            avgResponseTime: {{ performance_stats.average_response_time }},
            totalRequests: {{ performance_stats.total_requests }}
        });

        // Site Access Tab Functionality
        loadSiteAccessData();
    });

    // Function to load site access data
    function loadSiteAccessData() {
        const personId = '{{ user_profile.personid }}';

        console.log('🔍 Site Access: Using personId:', personId);

        // Load data when tabs are clicked
        document.getElementById('person-tab').addEventListener('click', function() {
            loadPersonData(personId);
        });

        document.getElementById('maxuser-tab').addEventListener('click', function() {
            loadMaxUserData(personId);
        });

        document.getElementById('groups-tab').addEventListener('click', function() {
            loadGroupsData(personId);
        });

        document.getElementById('sites-tab').addEventListener('click', function() {
            loadSitesData(personId);
        });

        // Load person data by default
        loadPersonData(personId);
    }

    // Load Person Data
    function loadPersonData(personId) {
        const container = document.getElementById('personData');

        fetch(`/api/site-access/${personId}/person`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    container.innerHTML = generatePersonTable(data.data);
                } else {
                    container.innerHTML = `<div class="alert alert-warning">No person data found</div>`;
                }
            })
            .catch(error => {
                container.innerHTML = `<div class="alert alert-danger">Error loading person data: ${error.message}</div>`;
            });
    }

    // Load MaxUser Data
    function loadMaxUserData(personId) {
        const container = document.getElementById('maxuserData');

        fetch(`/api/site-access/${personId}/maxuser`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    container.innerHTML = generateMaxUserTable(data.data);
                } else {
                    container.innerHTML = `<div class="alert alert-warning">No user account data found</div>`;
                }
            })
            .catch(error => {
                container.innerHTML = `<div class="alert alert-danger">Error loading user account data: ${error.message}</div>`;
            });
    }

    // Load Groups Data
    function loadGroupsData(personId) {
        const container = document.getElementById('groupsData');

        fetch(`/api/site-access/${personId}/groups`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    container.innerHTML = generateGroupsTable(data.data);
                } else {
                    container.innerHTML = `<div class="alert alert-warning">No group memberships found</div>`;
                }
            })
            .catch(error => {
                container.innerHTML = `<div class="alert alert-danger">Error loading group memberships: ${error.message}</div>`;
            });
    }

    // Load Sites Data
    function loadSitesData(personId) {
        const container = document.getElementById('sitesData');

        // First check if user has AUTHALLSITES=1
        fetch(`/api/site-access/${personId}/groups`)
            .then(response => response.json())
            .then(groupsData => {
                const hasAuthAllSites = groupsData.success &&
                    groupsData.data.some(group => group['AUTHALLSITES'] === '1');

                // Then fetch sites data
                return fetch(`/api/site-access/${personId}/sites`)
                    .then(response => response.json())
                    .then(sitesData => {
                        if (sitesData.success) {
                            container.innerHTML = generateSitesTable(sitesData.data, hasAuthAllSites);
                        } else {
                            container.innerHTML = `<div class="alert alert-warning">No site authorizations found</div>`;
                        }
                    });
            })
            .catch(error => {
                container.innerHTML = `<div class="alert alert-danger">Error loading site authorizations: ${error.message}</div>`;
            });
    }

    // Generate Person Table
    function generatePersonTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Field</th><th>Value</th></tr></thead><tbody>';

        // Use the capitalized field names from the API response
        Object.keys(data).forEach(field => {
            if (data[field] !== undefined) {
                html += `<tr><td><strong>${field}</strong></td><td>${data[field] || 'N/A'}</td></tr>`;
            }
        });

        html += '</tbody></table></div>';
        return html;
    }

    // Generate MaxUser Table
    function generateMaxUserTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Field</th><th>Value</th></tr></thead><tbody>';

        // Use the capitalized field names from the API response
        Object.keys(data).forEach(field => {
            if (data[field] !== undefined) {
                html += `<tr><td><strong>${field}</strong></td><td>${data[field] || 'N/A'}</td></tr>`;
            }
        });

        html += '</tbody></table></div>';
        return html;
    }

    // Generate Groups Table
    function generateGroupsTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Group Name</th><th>Description</th><th>AUTHALLSITES</th></tr></thead><tbody>';

        data.forEach(group => {
            const authallsites = group['AUTHALLSITES'];
            const authallsitesClass = authallsites === '1' ? 'text-success fw-bold' : 'text-muted';
            const authallsitesText = authallsites === '1' ? 'YES (All Sites)' : 'NO';

            html += `<tr>
                <td><strong>${group['Group Name']}</strong></td>
                <td>${group['Description'] || 'N/A'}</td>
                <td><span class="${authallsitesClass}">${authallsitesText}</span></td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        return html;
    }

    // Generate Sites Table
    function generateSitesTable(data, hasAuthAllSites = false) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Site ID</th><th>Organization</th></tr></thead><tbody>';

        if (data.length === 0) {
            html += '<tr><td colspan="2" class="text-center text-muted">No site authorizations found</td></tr>';
        } else {
            data.forEach(site => {
                html += `<tr><td><strong>${site['Site ID']}</strong></td><td>${site['Organization'] || 'N/A'}</td></tr>`;
            });
        }

        html += '</tbody></table></div>';

        // Add note about AUTHALLSITES if applicable
        if (hasAuthAllSites) {
            html += '<div class="alert alert-success mt-2"><i class="fas fa-key me-1"></i><strong>AUTHALLSITES Permission:</strong> This user has AUTHALLSITES=1 in one or more groups, showing all available sites from the entire system.</div>';
        }

        return html;
    }
</script>

<style>
    /* Mobile-friendly responsive design */
    @media (max-width: 768px) {
        .container-fluid {
            padding: 0.5rem;
        }

        .card {
            margin-bottom: 1rem;
        }

        .nav-tabs {
            flex-wrap: wrap;
        }

        .nav-tabs .nav-link {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .table-responsive {
            font-size: 0.9rem;
        }

        .btn {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        h1 {
            font-size: 1.5rem;
        }

        h2 {
            font-size: 1.3rem;
        }

        .card-body {
            padding: 1rem;
        }
    }

    @media (max-width: 576px) {
        .nav-tabs .nav-link {
            font-size: 0.8rem;
            padding: 0.4rem 0.6rem;
        }

        .table-responsive {
            font-size: 0.8rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            margin-bottom: 0.25rem;
        }

        .card-body {
            padding: 0.75rem;
        }

        .row .col-md-3 {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}