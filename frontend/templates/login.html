{% extends 'base.html' %}

{% block title %}Login - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center" style="min-height: 80vh;">
    <div class="col-md-6 col-lg-5 col-xl-4">
        <div class="text-center mb-4">
            <i class="fas fa-key fa-3x text-primary mb-3"></i>
            <h2 class="fw-bold"><PERSON><PERSON> OAuth</h2>
        </div>

        <div class="card shadow border-0">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <h5 class="card-title fw-bold">Login to Test (UAT) Maximo</h5>
                    <div class="badge bg-warning text-dark mb-3">UAT Environment</div>
                    <p class="card-text text-muted">Enter your credentials to access the system</p>
                </div>

                <form method="POST" action="{{ url_for('login') }}">
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="username" name="username" placeholder="Username" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-lock text-primary"></i>
                            </span>
                            <input type="password" class="form-control border-start-0" id="password" name="password" placeholder="Password" required>
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                        <label class="form-check-label" for="rememberMe">Remember me</label>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Lightning-Fast Login
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-center">
                    <div class="form-check form-switch d-inline-block">
                        <input class="form-check-input" type="checkbox" id="themeSwitch">
                        <label class="form-check-label" for="themeSwitch">Dark Mode</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-muted">Secure connection to Maximo UAT Environment</small>
        </div>
    </div>
</div>
{% endblock %}
