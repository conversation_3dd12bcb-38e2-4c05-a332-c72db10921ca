<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON>o OAuth <PERSON>gin{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if 'username' in session %}
    <!-- Top Header for all devices -->
    <header class="app-header">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="app-title">
                <a href="{{ url_for('welcome') }}">
                    <i class="fas fa-key me-2"></i>Maximo OAuth
                </a>
            </div>
            <div class="d-flex align-items-center">
                <div class="d-none d-md-flex me-3">
                    <a href="{{ url_for('welcome') }}" class="nav-link me-3">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="{{ url_for('profile') }}" class="nav-link me-3">
                        <i class="fas fa-user me-1"></i>Profile
                    </a>
                    <a href="{{ url_for('sync') }}" class="nav-link me-3">
                        <i class="fas fa-sync-alt me-1"></i>Sync
                    </a>
                    <a href="{{ url_for('enhanced_workorders') }}" class="nav-link me-3">
                        <i class="fas fa-clipboard-list me-1"></i>Work Orders
                    </a>
                </div>
                <span class="user-info d-none d-md-inline-block">
                    <i class="fas fa-user-circle me-1"></i>{{ session['username'] }}
                </span>
            </div>
        </div>
    </header>

    <!-- Bottom Navigation for Mobile -->
    <nav class="mobile-nav d-md-none">
        <a href="{{ url_for('welcome') }}" class="nav-link">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="{{ url_for('profile') }}" class="nav-link">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
        <a href="{{ url_for('sync') }}" class="nav-link">
            <i class="fas fa-sync-alt"></i>
            <span>Sync</span>
        </a>
        <a href="{{ url_for('enhanced_workorders') }}" class="nav-link">
            <i class="fas fa-clipboard-list"></i>
            <span>Work Orders</span>
        </a>
        <a href="{{ url_for('logout') }}" class="nav-link">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>
    </nav>
    {% endif %}

    <main class="app-content">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-0">Developed by Praba Krishna @2023</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
