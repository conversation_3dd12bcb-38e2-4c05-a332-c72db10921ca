#!/usr/bin/env python3
"""
Labor Deletion Service for Work Orders
Handles deletion and adjustment of labor entries using MXAPIWODETAIL API
"""

import logging
import time
import json
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)

class LaborDeletionService:
    """
    Service for deleting or adjusting labor entries in work orders.
    
    This service handles:
    - Complete deletion of labor entries
    - Partial hour adjustments using negative values
    - Session-based authentication using token manager
    - Proper payload construction following Maximo API requirements
    """
    
    def __init__(self, token_manager, task_labor_service=None):
        """Initialize the labor deletion service."""
        self.token_manager = token_manager
        self.task_labor_service = task_labor_service
        self.logger = logger
        
        # Performance tracking
        self._request_count = 0
        self._success_count = 0
        self._total_time = 0.0
        
    def delete_labor_entry(self, task_wonum: str, labor_trans_id: str, 
                          deletion_type: str = "entire", hours_to_subtract: float = None) -> Dict[str, Any]:
        """
        Delete or adjust a labor entry.
        
        Args:
            task_wonum: Task work order number
            labor_trans_id: Labor transaction ID to delete/adjust
            deletion_type: "entire" for complete deletion, "partial" for hour adjustment
            hours_to_subtract: Hours to subtract (only for partial deletion)
            
        Returns:
            Dict containing success status and response data
        """
        start_time = time.time()
        
        try:
            # Validate session
            if not self.is_session_valid():
                self.logger.error("Cannot delete labor: Not logged in")
                return {'success': False, 'error': 'Not logged in'}
            
            # Validate required parameters
            if not all([task_wonum, labor_trans_id, deletion_type]):
                missing = [param for param, value in [
                    ('task_wonum', task_wonum), ('labor_trans_id', labor_trans_id), 
                    ('deletion_type', deletion_type)
                ] if not value]
                self.logger.error(f"Missing required parameters: {missing}")
                return {'success': False, 'error': f'Missing required parameters: {missing}'}
            
            # Validate partial deletion parameters
            if deletion_type == "partial":
                if not hours_to_subtract or hours_to_subtract <= 0:
                    return {'success': False, 'error': 'Hours to subtract must be greater than 0 for partial deletion'}
            
            # Get the existing labor entry details
            labor_entry = self._get_labor_entry(task_wonum, labor_trans_id)
            if not labor_entry:
                return {'success': False, 'error': f'Labor entry {labor_trans_id} not found'}
            
            # Perform the deletion/adjustment
            if deletion_type == "entire":
                result = self._delete_entire_entry(labor_entry)
            else:  # partial
                result = self._adjust_labor_hours(labor_entry, hours_to_subtract)
            
            # Update performance stats
            request_time = time.time() - start_time
            self._update_performance_stats(request_time, result.get('success', False))
            
            # Clear cache after successful operation
            if result.get('success') and self.task_labor_service:
                self.task_labor_service.clear_cache()
            
            return result
            
        except Exception as e:
            request_time = time.time() - start_time
            self._update_performance_stats(request_time, False)
            self.logger.error(f"Exception in delete_labor_entry: {e}")
            return {'success': False, 'error': f'Unexpected error: {str(e)}'}
    
    def _get_labor_entry(self, task_wonum: str, labor_trans_id: str) -> Optional[Dict]:
        """Get the labor entry details."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapilabtrans"
            
            params = {
                'oslc.where': f'labtransid="{labor_trans_id}" and wonum="{task_wonum}"',
                'oslc.select': '*',
                'lean': '1'
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data and data['member']:
                    return data['member'][0]
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting labor entry: {e}")
            return None
    
    def _delete_entire_entry(self, labor_entry: Dict) -> Dict[str, Any]:
        """Delete the entire labor entry using DELETE method."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            labor_href = labor_entry.get('href')
            
            if not labor_href:
                return {'success': False, 'error': 'Labor entry href not found'}
            
            # Use the href to delete the entry
            response = self.token_manager.session.delete(
                labor_href,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code in [200, 204]:
                return {
                    'success': True,
                    'message': f'Labor entry {labor_entry.get("labtransid")} deleted successfully',
                    'deletion_type': 'entire'
                }
            else:
                error_msg = f"Failed to delete labor entry: HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'Error' in error_data:
                        error_msg = error_data['Error'].get('message', error_msg)
                except:
                    pass
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            self.logger.error(f"Error deleting labor entry: {e}")
            return {'success': False, 'error': f'Failed to delete labor entry: {str(e)}'}
    
    def _adjust_labor_hours(self, labor_entry: Dict, hours_to_subtract: float) -> Dict[str, Any]:
        """Adjust labor hours by adding a negative entry."""
        try:
            # Create a negative labor entry to offset the hours
            wonum = labor_entry.get('wonum')
            siteid = labor_entry.get('siteid')
            laborcode = labor_entry.get('laborcode')
            taskid = labor_entry.get('taskid')
            craft = labor_entry.get('craft')
            
            # Create negative hours entry
            negative_hours = -abs(hours_to_subtract)
            
            # Build the payload for adding negative hours
            labor_payload = {
                "laborcode": laborcode,
                "regularhrs": negative_hours,
                "taskid": taskid,
                "craft": craft or "",
                "transtype": "WORK"
            }
            
            # Get work order data for AddChange
            wo_data = self._get_work_order_data(wonum, siteid)
            if not wo_data:
                return {'success': False, 'error': f'Work order {wonum} not found'}
            
            # Create AddChange payload
            addchange_payload = {
                "wonum": wonum,
                "siteid": siteid,
                "labtrans": [labor_payload]
            }
            
            # Make the API request
            result = self._make_addchange_request(addchange_payload)
            
            if result.get('success'):
                result['deletion_type'] = 'partial'
                result['hours_subtracted'] = hours_to_subtract
                result['message'] = f'Subtracted {hours_to_subtract} hours from labor entry'
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error adjusting labor hours: {e}")
            return {'success': False, 'error': f'Failed to adjust labor hours: {str(e)}'}
    
    def _get_work_order_data(self, wonum: str, siteid: str) -> Optional[Dict]:
        """Get work order data for AddChange operation."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                'oslc.where': f'wonum="{wonum}" and siteid="{siteid}"',
                'oslc.select': '*',
                'lean': '1'
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data and data['member']:
                    return data['member'][0]
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting work order data: {e}")
            return None
    
    def _make_addchange_request(self, payload: Dict) -> Dict[str, Any]:
        """Make AddChange request to add negative hours."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                'lean': '1',
                'ignorecollectionref': '1',
                'ignorekeyref': '1',
                'ignorers': '1',
                'mxlaction': 'addchange'
            }
            
            headers = {
                'x-method-override': 'BULK',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            self.logger.info(f"🔧 LABOR DELETION: Making AddChange request for negative hours")
            self.logger.debug(f"🔧 LABOR DELETION: Payload: {json.dumps(payload, indent=2)}")
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                params=params,
                headers=headers,
                timeout=(3.05, 30)
            )
            
            return self._process_addchange_response(response)
            
        except Exception as e:
            self.logger.error(f"Error making AddChange request: {e}")
            return {'success': False, 'error': f'Request failed: {str(e)}'}
    
    def _process_addchange_response(self, response) -> Dict[str, Any]:
        """Process the AddChange response."""
        try:
            if response.status_code != 200:
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}
            
            result_data = response.json()
            
            if isinstance(result_data, list) and len(result_data) > 0:
                response_data = result_data[0]
                if '_responsedata' in response_data and 'Error' in response_data['_responsedata']:
                    error = response_data['_responsedata']['Error']
                    error_message = error.get('message', 'Unknown error')
                    error_code = error.get('reasonCode', 'Unknown code')
                    return {
                        'success': False,
                        'error': f"Maximo Error [{error_code}]: {error_message}",
                        'error_code': error_code
                    }
                elif '_responsemeta' in response_data and response_data['_responsemeta'].get('status') == '204':
                    return {
                        'success': True,
                        'message': 'Labor hours adjusted successfully',
                        'data': result_data
                    }
            
            return {'success': False, 'error': 'Unexpected response format'}
            
        except Exception as e:
            self.logger.error(f"Error processing response: {e}")
            return {'success': False, 'error': f'Response processing failed: {str(e)}'}
    
    def is_session_valid(self) -> bool:
        """Check if the session is valid."""
        return (hasattr(self.token_manager, 'username') and 
                self.token_manager.username and 
                hasattr(self.token_manager, 'session') and 
                self.token_manager.session)
    
    def _update_performance_stats(self, request_time: float, success: bool):
        """Update performance statistics."""
        self._request_count += 1
        self._total_time += request_time
        if success:
            self._success_count += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_time = self._total_time / self._request_count if self._request_count > 0 else 0
        success_rate = (self._success_count / self._request_count * 100) if self._request_count > 0 else 0
        
        return {
            'total_requests': self._request_count,
            'successful_requests': self._success_count,
            'success_rate_percent': round(success_rate, 2),
            'average_response_time_seconds': round(avg_time, 3),
            'total_time_seconds': round(self._total_time, 3)
        }
