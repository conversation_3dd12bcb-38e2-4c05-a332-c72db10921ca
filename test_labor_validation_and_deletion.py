#!/usr/bin/env python3
"""
Test script to verify labor validation and deletion functionality
"""

import re
import os
import json

def test_frontend_validation():
    """Test that frontend validation has been restored"""
    print("🔍 Testing frontend validation...")
    
    try:
        # Read the labor_search_modal.html file
        with open('frontend/templates/components/labor_search_modal.html', 'r') as f:
            template_content = f.read()
        
        # Find the laborHours input field
        labor_hours_pattern = r'<input[^>]*id="laborHours"[^>]*>'
        labor_hours_match = re.search(labor_hours_pattern, template_content)
        
        if not labor_hours_match:
            print("❌ laborHours input field not found")
            return False
        
        labor_hours_input = labor_hours_match.group(0)
        print(f"Found laborHours input: {labor_hours_input}")
        
        # Check that min="0.01" is present
        if 'min="0.01"' in labor_hours_input:
            print("✅ Frontend validation min='0.01' is present")
            return True
        else:
            print("❌ Frontend validation min='0.01' is missing")
            return False
            
    except Exception as e:
        print(f"❌ Error testing frontend validation: {e}")
        return False

def test_labor_display_delete_buttons():
    """Test that delete buttons have been added to labor display"""
    print("\n🔍 Testing labor display delete buttons...")
    
    try:
        # Read the workorder_detail.html file
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Check for delete buttons in desktop view
        desktop_delete_pattern = r'<button[^>]*class="[^"]*delete-labor-btn[^"]*"[^>]*onclick="deleteLaborEntry\(this\)"[^>]*>'
        desktop_matches = re.findall(desktop_delete_pattern, template_content)
        
        if len(desktop_matches) >= 1:
            print(f"✅ Found {len(desktop_matches)} delete button(s) in labor display")
            print(f"Sample button: {desktop_matches[0][:100]}...")
        else:
            print("❌ Delete buttons not found in labor display")
            return False
        
        # Check for Actions column header
        if '<th>Actions</th>' in template_content:
            print("✅ Actions column header found in desktop table")
        else:
            print("❌ Actions column header missing from desktop table")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing labor display: {e}")
        return False

def test_labor_deletion_modal():
    """Test that labor deletion modal has been created"""
    print("\n🔍 Testing labor deletion modal...")
    
    try:
        # Read the labor_search_modal.html file
        with open('frontend/templates/components/labor_search_modal.html', 'r') as f:
            template_content = f.read()
        
        # Check for deletion modal
        if 'id="laborDeletionModal"' in template_content:
            print("✅ Labor deletion modal found")
        else:
            print("❌ Labor deletion modal not found")
            return False
        
        # Check for deletion options
        if 'name="deletionType"' in template_content:
            print("✅ Deletion type radio buttons found")
        else:
            print("❌ Deletion type radio buttons not found")
            return False
        
        # Check for partial hours section
        if 'id="partialHoursSection"' in template_content:
            print("✅ Partial hours section found")
        else:
            print("❌ Partial hours section not found")
            return False
        
        # Check for confirm button
        if 'id="confirmLaborDeletion"' in template_content:
            print("✅ Confirm deletion button found")
        else:
            print("❌ Confirm deletion button not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing deletion modal: {e}")
        return False

def test_backend_deletion_service():
    """Test that backend deletion service has been created"""
    print("\n🔍 Testing backend deletion service...")
    
    try:
        # Check if deletion service file exists
        if os.path.exists('backend/services/labor_deletion_service.py'):
            print("✅ Labor deletion service file exists")
        else:
            print("❌ Labor deletion service file not found")
            return False
        
        # Read the service file
        with open('backend/services/labor_deletion_service.py', 'r') as f:
            service_content = f.read()
        
        # Check for key methods
        required_methods = [
            'delete_labor_entry',
            '_delete_entire_entry',
            '_adjust_labor_hours',
            '_make_addchange_request'
        ]
        
        for method in required_methods:
            if f'def {method}(' in service_content:
                print(f"✅ Method {method} found")
            else:
                print(f"❌ Method {method} not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing deletion service: {e}")
        return False

def test_api_endpoint():
    """Test that API endpoint has been added"""
    print("\n🔍 Testing API endpoint...")
    
    try:
        # Read the app.py file
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        # Check for deletion endpoint
        if '@app.route(\'/api/task/<task_wonum>/delete-labor\', methods=[\'POST\'])' in app_content:
            print("✅ Labor deletion API endpoint found")
        else:
            print("❌ Labor deletion API endpoint not found")
            return False
        
        # Check for deletion service import
        if 'from backend.services.labor_deletion_service import LaborDeletionService' in app_content:
            print("✅ Labor deletion service import found")
        else:
            print("❌ Labor deletion service import not found")
            return False
        
        # Check for service initialization
        if 'labor_deletion_service = LaborDeletionService(' in app_content:
            print("✅ Labor deletion service initialization found")
        else:
            print("❌ Labor deletion service initialization not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
        return False

def test_javascript_handlers():
    """Test that JavaScript handlers have been added"""
    print("\n🔍 Testing JavaScript handlers...")
    
    try:
        # Read the labor_search.js file
        with open('frontend/static/js/labor_search.js', 'r') as f:
            js_content = f.read()
        
        # Check for LaborDeletionManager class
        if 'class LaborDeletionManager' in js_content:
            print("✅ LaborDeletionManager class found")
        else:
            print("❌ LaborDeletionManager class not found")
            return False
        
        # Check for key methods
        required_methods = [
            'showDeletionModal',
            'confirmDeletion',
            'updateDeleteButton',
            'refreshLaborDisplay'
        ]
        
        for method in required_methods:
            if f'{method}(' in js_content:
                print(f"✅ Method {method} found")
            else:
                print(f"❌ Method {method} not found")
                return False
        
        # Check for global function
        if 'function deleteLaborEntry(' in js_content:
            print("✅ Global deleteLaborEntry function found")
        else:
            print("❌ Global deleteLaborEntry function not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing JavaScript handlers: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Labor Validation and Deletion Implementation")
    print("=" * 60)
    
    tests = [
        ("Frontend Validation", test_frontend_validation),
        ("Labor Display Delete Buttons", test_labor_display_delete_buttons),
        ("Labor Deletion Modal", test_labor_deletion_modal),
        ("Backend Deletion Service", test_backend_deletion_service),
        ("API Endpoint", test_api_endpoint),
        ("JavaScript Handlers", test_javascript_handlers)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Labor validation and deletion implementation is complete.")
        print("\n📋 Implementation Summary:")
        print("✅ Frontend validation restored (min='0.01' for labor hours)")
        print("✅ Delete buttons added to labor display (desktop and mobile)")
        print("✅ Labor deletion modal created with entire/partial options")
        print("✅ Backend deletion service implemented")
        print("✅ API endpoint for labor deletion added")
        print("✅ JavaScript handlers for deletion workflow implemented")
        print("\n🔧 User Workflow:")
        print("1. Users cannot enter negative hours directly (frontend validation)")
        print("2. Users can click 'Delete' button on existing labor entries")
        print("3. Modal offers options: delete entire entry or subtract hours")
        print("4. Backend handles both complete deletion and partial adjustments")
        print("5. Labor display refreshes automatically after deletion")
        return True
    else:
        print(f"❌ {total - passed} test(s) failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    main()
